# Arduino Controller - Relay Configuration Guide

## 🔌 Relay Active HIGH vs Active LOW

### ⚡ **Current Configuration: Active LOW**

The Arduino code has been updated to support **Active LOW** relays (most common type).

### 📊 **Active LOW Relay Logic:**
```cpp
// تشغيل الريلاي (Relay ON)
digitalWrite(RELAY_PIN, LOW);   // LOW = ON

// إيقاف الريلاي (Relay OFF)  
digitalWrite(RELAY_PIN, HIGH);  // HIGH = OFF
```

### 📊 **Active HIGH Relay Logic:**
```cpp
// تشغيل الريلاي (Relay ON)
digitalWrite(RELAY_PIN, HIGH);  // HIGH = ON

// إيقاف الريلاي (Relay OFF)
digitalWrite(RELAY_PIN, LOW);   // LOW = OFF
```

## 🔍 **How to Identify Your Relay Type**

### **Active LOW Relay (Most Common):**
- ✅ **Relay activates when pin goes LOW**
- ✅ **Usually has built-in LED that lights when relay is OFF**
- ✅ **Common in relay modules from China**
- ✅ **Examples**: Most 1-channel, 2-channel, 4-channel relay modules

### **Active HIGH Relay (Less Common):**
- ⚠️ **Relay activates when pin goes HIGH**
- ⚠️ **LED lights when relay is ON**
- ⚠️ **Usually more expensive modules**
- ⚠️ **Examples**: Some industrial relay modules

## 🧪 **Testing Your Relay Type**

### **Method 1: Visual Test**
1. **Connect relay to Arduino**
2. **Upload the code**
3. **Use Python GUI to turn relay ON**
4. **Listen for relay click sound**
5. **Check if connected device activates**

### **Method 2: LED Indicator Test**
1. **Most relay modules have LED indicators**
2. **Send RELAY_RIGHT_ON command**
3. **If LED turns ON → Active LOW (current code)**
4. **If LED turns OFF → Active HIGH (need to change code)**

### **Method 3: Multimeter Test**
1. **Measure voltage between COM and NO pins**
2. **Send RELAY_RIGHT_ON command**
3. **If continuity appears → Relay is working correctly**
4. **If no continuity → Wrong relay type**

## 🔧 **Current Code Configuration**

### **Pin Assignments:**
```cpp
const int RELAY_RIGHT_PIN = 10;  // Right relay (Pin D10)
const int RELAY_LEFT_PIN = 11;   // Left relay (Pin D11)
```

### **Initialization (Active LOW):**
```cpp
void setup() {
  pinMode(RELAY_RIGHT_PIN, OUTPUT);
  pinMode(RELAY_LEFT_PIN, OUTPUT);
  
  // Initialize relays to OFF state (HIGH for Active LOW)
  digitalWrite(RELAY_RIGHT_PIN, HIGH);  // OFF
  digitalWrite(RELAY_LEFT_PIN, HIGH);   // OFF
}
```

### **Relay Control Functions:**
```cpp
// Turn ON Right Relay
digitalWrite(RELAY_RIGHT_PIN, LOW);   // LOW = ON for Active LOW

// Turn OFF Right Relay  
digitalWrite(RELAY_RIGHT_PIN, HIGH);  // HIGH = OFF for Active LOW

// Turn ON Left Relay
digitalWrite(RELAY_LEFT_PIN, LOW);    // LOW = ON for Active LOW

// Turn OFF Left Relay
digitalWrite(RELAY_LEFT_PIN, HIGH);   // HIGH = OFF for Active LOW
```

## 🔄 **If You Have Active HIGH Relays**

If your relays are **Active HIGH**, you need to change the code:

### **Change 1: Initialization**
```cpp
// In setup() function:
digitalWrite(RELAY_RIGHT_PIN, LOW);   // LOW = OFF for Active HIGH
digitalWrite(RELAY_LEFT_PIN, LOW);    // LOW = OFF for Active HIGH
```

### **Change 2: Relay ON Commands**
```cpp
// RELAY_RIGHT_ON command:
digitalWrite(RELAY_RIGHT_PIN, HIGH);  // HIGH = ON for Active HIGH

// RELAY_LEFT_ON command:
digitalWrite(RELAY_LEFT_PIN, HIGH);   // HIGH = ON for Active HIGH
```

### **Change 3: Relay OFF Commands**
```cpp
// RELAY_RIGHT_OFF command:
digitalWrite(RELAY_RIGHT_PIN, LOW);   // LOW = OFF for Active HIGH

// RELAY_LEFT_OFF command:
digitalWrite(RELAY_LEFT_PIN, LOW);    // LOW = OFF for Active HIGH
```

### **Change 4: Auto-OFF Timer**
```cpp
// In loop() function:
if (relayRightActive && relayRightDeactivationTime != 0 && currentTime >= relayRightDeactivationTime) {
  digitalWrite(RELAY_RIGHT_PIN, LOW);  // LOW = OFF for Active HIGH
  relayRightActive = false;
  relayRightDeactivationTime = 0;
}

if (relayLeftActive && relayLeftDeactivationTime != 0 && currentTime >= relayLeftDeactivationTime) {
  digitalWrite(RELAY_LEFT_PIN, LOW);   // LOW = OFF for Active HIGH
  relayLeftActive = false;
  relayLeftDeactivationTime = 0;
}
```

## 🎯 **Testing Commands**

### **Test Relay with Python GUI:**
1. **Connect to Arduino**
2. **Go to "Relay Control" tab**
3. **Set timer to 2 seconds**
4. **Click "Turn On" for right relay**
5. **Listen for relay click**
6. **Watch for auto-off after 2 seconds**

### **Test Relay with Serial Monitor:**
```
RELAY_RIGHT_ON,2    # Turn on for 2 seconds
RELAY_RIGHT_OFF     # Turn off immediately
RELAY_LEFT_ON,0     # Turn on manually (no timer)
RELAY_LEFT_OFF      # Turn off manually
```

### **Expected Responses:**
```
RELAY_RIGHT_ON      # Confirmation
RELAY_RIGHT_AUTO_OFF # After timer expires
RELAY_RIGHT_OFF     # Manual off confirmation
```

## ⚠️ **Safety Considerations**

### **Important Safety Rules:**
1. **Never connect AC mains directly** without proper isolation
2. **Use appropriate fuses** for your load
3. **Check relay current rating** vs your load
4. **Test with low voltage first** (12V DC LED strip)
5. **Use timer mode** to prevent accidental long activation

### **Recommended Test Setup:**
```
Arduino → Relay Module → 12V DC LED Strip → 12V Power Supply
```

### **Wiring:**
```
Arduino Pin 10 → Relay Module IN1 (Right Relay)
Arduino Pin 11 → Relay Module IN2 (Left Relay)
Arduino GND → Relay Module GND
Arduino 5V → Relay Module VCC

Relay COM → 12V+ (Power Supply)
Relay NO → LED Strip + (Positive)
LED Strip - → 12V- (Power Supply Ground)
```

## 🎉 **Success Indicators**

### **Relay Working Correctly:**
- ✅ **Audible click** when relay activates
- ✅ **LED indicator** changes state
- ✅ **Connected device** turns on/off
- ✅ **Timer function** works (auto-off)
- ✅ **Python GUI** shows correct status

### **Troubleshooting:**
- ❌ **No click sound** → Check wiring or relay type
- ❌ **LED doesn't change** → Wrong Active HIGH/LOW setting
- ❌ **Device doesn't activate** → Check load connections
- ❌ **Timer doesn't work** → Check Arduino code upload

## 📞 **Quick Reference**

### **Current Code (Active LOW):**
- `HIGH` = Relay OFF
- `LOW` = Relay ON

### **If You Need Active HIGH:**
- `HIGH` = Relay ON  
- `LOW` = Relay OFF

### **Test Commands:**
```
RELAY_RIGHT_ON,5    # Right relay ON for 5 seconds
RELAY_LEFT_ON,0     # Left relay ON manual mode
RELAY_RIGHT_OFF     # Right relay OFF immediately
RELAY_LEFT_OFF      # Left relay OFF immediately
```

**Your relay configuration is now optimized for most common relay modules!** 🔌
