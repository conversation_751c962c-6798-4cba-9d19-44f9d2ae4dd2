#include <Arduino.h>
#include <EEPROM.h>
#include <Stepper.h>

// Pin Definitions
const int RED_LED_PIN = 9;
const int GREEN_LED_PIN = 5;
const int BLUE_LED_PIN = 6;
const int PULSE_PIN = 8;

// LED Matrix Pins (for testing)
const int LED_MATRIX_PIN = 13; // Built-in LED for simulation

// Relay Pins
const int RELAY_RIGHT_PIN = 10;
const int RELAY_LEFT_PIN = 11;

// Synchronized Flash and Camera Pins
const int FLASH_TRIGGER_PIN = 12;  // Flash trigger output
const int CAMERA_TRIGGER_PIN = 7;  // Camera trigger output (digital pin)

// Stepper Motor Pins
const int STEPPER_PIN_IN1 = 2;
const int STEPPER_PIN_IN2 = 3;
const int STEPPER_PIN_IN3 = 4;
const int STEPPER_PIN_IN4 = 7;

const int STEPS_PER_REVOLUTION_MOTOR = 2048; // For 28BYJ-48
Stepper myStepper(STEPS_PER_REVOLUTION_MOTOR, STEPPER_PIN_IN1, STEPPER_PIN_IN3, STEPPER_PIN_IN2, STEPPER_PIN_IN4);

// Serial Communication Settings
const unsigned long SERIAL_BAUD_RATE = 115200;

// Global Variables
int currentPwmValues[3] = {0, 0, 0};
long stepperTotalSteps = 0;
float currentStepperAngle = 0.0;
int stepperSpeedRPM = 12; 
enum StepperMode { STEPPER_IDLE, STEPPER_GOTO_INTERNAL, STEPPER_CW, STEPPER_CCW };
StepperMode currentStepperMode = STEPPER_IDLE;
long stepperTargetTotalSteps = 0;

// Relay States and Timers
bool relayRightActive = false;
unsigned long relayRightDeactivationTime = 0; // 0 means manual mode or off
unsigned int relayRightTimerSettingSec = 5;   // Default timer in seconds

bool relayLeftActive = false;
unsigned long relayLeftDeactivationTime = 0;  // 0 means manual mode or off
unsigned int relayLeftTimerSettingSec = 5;    // Default timer in seconds

// EEPROM Settings Structure
struct Settings {
  int pwmValues[3];
  int shootRateHz;
  int stepperSpeedRPM_eeprom;
  unsigned int relayRightTimerSec_eeprom;
  unsigned int relayLeftTimerSec_eeprom;
  unsigned long magic;
};
Settings currentStoredSettings;
const unsigned long EEPROM_MAGIC_NUMBER = 0xABCD1234;
const int EEPROM_SETTINGS_ADDR = 0;

// Shooting Variables
bool continuousShootingActive = false;
int shootRateHz = 1;
unsigned long lastPulseTime = 0;
unsigned long pulseIntervalMillis = 1000;

// Synchronized Flash-Camera System Variables
unsigned long flashPreDelay = 50;        // Flash pre-fire delay (ms)
unsigned long cameraTriggerDelay = 101;   // Camera trigger delay after flash (ms) - based on communication delay
unsigned long flashDuration = 100;       // Flash pulse duration (ms)
unsigned long cameraDuration = 50;        // Camera trigger pulse duration (ms)
bool syncSystemActive = false;
unsigned long syncStartTime = 0;
enum SyncState { SYNC_IDLE, SYNC_FLASH_TRIGGERED, SYNC_CAMERA_TRIGGERED, SYNC_COMPLETE };
SyncState currentSyncState = SYNC_IDLE;

// Serial Command Processing
String inputString = "";
bool stringComplete = false;

// Function Declarations
void processSerialCommand(String command);
void sendResponse(String response);
void sendStepperStatus();
void sendRelayStatus();
void sendPwmValues();
void updateStepperAngleFromTotal();
String getStepperModeString();
void saveSettings();
void loadSettings();

void setup() {
  Serial.begin(SERIAL_BAUD_RATE);
  
  // Initialize pins
  pinMode(RED_LED_PIN, OUTPUT);
  pinMode(GREEN_LED_PIN, OUTPUT);
  pinMode(BLUE_LED_PIN, OUTPUT);
  pinMode(PULSE_PIN, OUTPUT);
  pinMode(RELAY_RIGHT_PIN, OUTPUT);
  pinMode(RELAY_LEFT_PIN, OUTPUT);
  pinMode(LED_MATRIX_PIN, OUTPUT);
  pinMode(FLASH_TRIGGER_PIN, OUTPUT);
  pinMode(CAMERA_TRIGGER_PIN, OUTPUT);

  // Initialize outputs to OFF
  digitalWrite(RED_LED_PIN, LOW);
  digitalWrite(GREEN_LED_PIN, LOW);
  digitalWrite(BLUE_LED_PIN, LOW);
  digitalWrite(PULSE_PIN, LOW);
  digitalWrite(RELAY_RIGHT_PIN, HIGH);  // HIGH = OFF for Active LOW relays
  digitalWrite(RELAY_LEFT_PIN, HIGH);   // HIGH = OFF for Active LOW relays
  digitalWrite(LED_MATRIX_PIN, LOW);
  digitalWrite(FLASH_TRIGGER_PIN, LOW);
  digitalWrite(CAMERA_TRIGGER_PIN, LOW);
  
  // Load settings from EEPROM
  loadSettings();
  
  // Set initial stepper speed
  myStepper.setSpeed(stepperSpeedRPM);
  
  // Reserve string for serial input
  inputString.reserve(200);
  
  Serial.println("Arduino Serial Controller Ready - v2.1 - Synchronized Flash-Camera System");
  Serial.println("Pin Configuration:");
  Serial.println("  Flash Trigger: Pin 12 | Camera Trigger: Pin 7");
  Serial.println("  PWM LEDs: 9(Red), 5(Green), 6(Blue) | Pulse: Pin 8");
  Serial.println("  Relays: 10(Right), 11(Left) | LED Matrix: Pin 13");
  Serial.println("Commands: SET_PWM, GET_PWM, SINGLE_SHOOT, START_SHOOT, STOP_SHOOT, SET_RATE");
  Serial.println("          STEPPER_CW, STEPPER_CCW, STEPPER_STOP, STEPPER_ANGLE, STEPPER_SPEED");
  Serial.println("          RELAY_RIGHT_ON, RELAY_RIGHT_OFF, RELAY_LEFT_ON, RELAY_LEFT_OFF");
  Serial.println("          LED_MATRIX_ON, LED_MATRIX_OFF, LED_MATRIX_PATTERN, LED_MATRIX_BLINK");
  Serial.println("          GET_STEPPER, GET_RELAY, SAVE, RESET, PING, INFO");
  Serial.println("Sync Commands: SYNC_PHOTO, SET_FLASH_DELAY, SET_CAMERA_DELAY, SET_FLASH_DURATION");
  Serial.println("               GET_SYNC_STATUS, SYNC_TEST_FLASH, SYNC_TEST_CAMERA");
}

void loop() {
  // Handle serial communication - check for incoming data
  while (Serial.available()) {
    char inChar = (char)Serial.read();

    if (inChar == '\n' || inChar == '\r') {
      if (inputString.length() > 0) {
        processSerialCommand(inputString);
        inputString = "";
      }
    } else {
      inputString += inChar;
    }
  }

  // Also handle stringComplete flag (backup method)
  if (stringComplete) {
    processSerialCommand(inputString);
    inputString = "";
    stringComplete = false;
  }
  
  // Handle continuous shooting
  if (continuousShootingActive && pulseIntervalMillis > 0) {
    unsigned long currentTime = millis();
    if (currentTime - lastPulseTime >= pulseIntervalMillis) {
      digitalWrite(PULSE_PIN, HIGH);
      delay(50);
      digitalWrite(PULSE_PIN, LOW);
      lastPulseTime = currentTime;
    }
  }
  
  // Handle stepper motor continuous movement
  if (currentStepperMode == STEPPER_CW) {
    myStepper.step(1);
    stepperTotalSteps++;
    updateStepperAngleFromTotal();
  } else if (currentStepperMode == STEPPER_CCW) {
    myStepper.step(-1);
    stepperTotalSteps--;
    updateStepperAngleFromTotal();
  }
  
  // Handle synchronized flash-camera system
  if (syncSystemActive) {
    unsigned long currentTime = millis();
    unsigned long elapsedTime = currentTime - syncStartTime;

    switch (currentSyncState) {
      case SYNC_FLASH_TRIGGERED:
        // Check if it's time to trigger camera
        if (elapsedTime >= cameraTriggerDelay) {
          digitalWrite(CAMERA_TRIGGER_PIN, HIGH);
          currentSyncState = SYNC_CAMERA_TRIGGERED;
          Serial.println("CAMERA_TRIGGERED");
        }
        break;

      case SYNC_CAMERA_TRIGGERED:
        // Check if flash duration is complete
        if (elapsedTime >= flashDuration) {
          digitalWrite(FLASH_TRIGGER_PIN, LOW);
        }
        // Check if camera duration is complete
        if (elapsedTime >= (cameraTriggerDelay + cameraDuration)) {
          digitalWrite(CAMERA_TRIGGER_PIN, LOW);
          currentSyncState = SYNC_COMPLETE;
          syncSystemActive = false;
          Serial.println("SYNC_PHOTO_COMPLETE");
        }
        break;

      default:
        break;
    }
  }

  // Handle relay timers
  unsigned long currentTime = millis();

  if (relayRightActive && relayRightDeactivationTime != 0 && currentTime >= relayRightDeactivationTime) {
    digitalWrite(RELAY_RIGHT_PIN, HIGH);  // HIGH = OFF for Active LOW relays
    relayRightActive = false;
    relayRightDeactivationTime = 0;
    Serial.println("RELAY_RIGHT_AUTO_OFF");
  }

  if (relayLeftActive && relayLeftDeactivationTime != 0 && currentTime >= relayLeftDeactivationTime) {
    digitalWrite(RELAY_LEFT_PIN, HIGH);   // HIGH = OFF for Active LOW relays
    relayLeftActive = false;
    relayLeftDeactivationTime = 0;
    Serial.println("RELAY_LEFT_AUTO_OFF");
  }
}

void serialEvent() {
  while (Serial.available()) {
    char inChar = (char)Serial.read();

    if (inChar == '\n' || inChar == '\r') {
      if (inputString.length() > 0) {
        stringComplete = true;
      }
    } else {
      inputString += inChar;
    }
  }
}

// Helper Functions
void sendResponse(String response) {
  Serial.println(response);
}

void updateStepperAngleFromTotal() {
  currentStepperAngle = fmod(((float)stepperTotalSteps / STEPS_PER_REVOLUTION_MOTOR) * 360.0, 360.0);
  if (currentStepperAngle < 0) currentStepperAngle += 360.0;
}

String getStepperModeString() {
  switch(currentStepperMode) {
    case STEPPER_IDLE: return "IDLE";
    case STEPPER_GOTO_INTERNAL: return "GOTO";
    case STEPPER_CW: return "CW";
    case STEPPER_CCW: return "CCW";
    default: return "UNKNOWN";
  }
}

void sendStepperStatus() {
  updateStepperAngleFromTotal();
  String response = "STEPPER_STATUS:";
  response += String(currentStepperAngle, 1) + ",";
  response += String(stepperSpeedRPM) + ",";
  response += getStepperModeString();
  Serial.println(response);
}

void sendRelayStatus() {
  String response = "RELAY_STATUS:";
  response += relayRightActive ? "1" : "0";
  response += ",";
  response += String(relayRightTimerSettingSec);
  response += ",";
  if (relayRightActive && relayRightDeactivationTime != 0) {
    long remaining = (long)relayRightDeactivationTime - (long)millis();
    response += String(remaining > 0 ? remaining : 0);
  } else {
    response += "0";
  }
  response += ",";
  response += relayLeftActive ? "1" : "0";
  response += ",";
  response += String(relayLeftTimerSettingSec);
  response += ",";
  if (relayLeftActive && relayLeftDeactivationTime != 0) {
    long remaining = (long)relayLeftDeactivationTime - (long)millis();
    response += String(remaining > 0 ? remaining : 0);
  } else {
    response += "0";
  }
  Serial.println(response);
}

void sendPwmValues() {
  String response = "PWM_VALUES:";
  for (int i = 0; i < 3; i++) {
    response += String(currentPwmValues[i]);
    if (i < 2) response += ",";
  }
  Serial.println(response);
}

// EEPROM Functions
void saveSettings() {
  Serial.println("Saving settings to EEPROM...");
  for (int i = 0; i < 3; i++) currentStoredSettings.pwmValues[i] = currentPwmValues[i];
  currentStoredSettings.shootRateHz = shootRateHz;
  currentStoredSettings.stepperSpeedRPM_eeprom = stepperSpeedRPM;
  currentStoredSettings.relayRightTimerSec_eeprom = relayRightTimerSettingSec;
  currentStoredSettings.relayLeftTimerSec_eeprom = relayLeftTimerSettingSec;
  currentStoredSettings.magic = EEPROM_MAGIC_NUMBER;
  EEPROM.put(EEPROM_SETTINGS_ADDR, currentStoredSettings);
  Serial.println("Settings saved.");
}

void loadSettings() {
  Serial.println("Loading settings from EEPROM...");
  EEPROM.get(EEPROM_SETTINGS_ADDR, currentStoredSettings);
  if (currentStoredSettings.magic != EEPROM_MAGIC_NUMBER) {
    Serial.println("EEPROM uninitialized. Loading defaults.");
    for (int i = 0; i < 3; i++) currentPwmValues[i] = 0;
    shootRateHz = 1;
    stepperSpeedRPM = 12; 
    relayRightTimerSettingSec = 5;
    relayLeftTimerSettingSec = 5;
    saveSettings();
  } else {
    Serial.println("Valid settings found.");
    for (int i = 0; i < 3; i++) currentPwmValues[i] = currentStoredSettings.pwmValues[i];
    shootRateHz = currentStoredSettings.shootRateHz;
    stepperSpeedRPM = currentStoredSettings.stepperSpeedRPM_eeprom;
    relayRightTimerSettingSec = currentStoredSettings.relayRightTimerSec_eeprom;
    relayLeftTimerSettingSec = currentStoredSettings.relayLeftTimerSec_eeprom;

    if (stepperSpeedRPM < 1 || stepperSpeedRPM > 20) stepperSpeedRPM = 12; 
    if (relayRightTimerSettingSec > 3600) relayRightTimerSettingSec = 5;
    if (relayLeftTimerSettingSec > 3600) relayLeftTimerSettingSec = 5;
  }
  if (shootRateHz > 0) pulseIntervalMillis = 1000 / shootRateHz;
  else {pulseIntervalMillis = 0; continuousShootingActive = false;}
  myStepper.setSpeed(stepperSpeedRPM); 
  Serial.print("Loaded: PWMs=");
  for(int i=0; i<3; i++) { Serial.print(currentPwmValues[i]); Serial.print(i==2?"":", "); }
  Serial.print(" | Rate="); Serial.print(shootRateHz);
  Serial.print(" | Stepper RPM="); Serial.print(stepperSpeedRPM);
  Serial.print(" | Relay Timers (R/L): "); Serial.print(relayRightTimerSettingSec); 
  Serial.print("s/"); Serial.print(relayLeftTimerSettingSec); Serial.println("s");
}

void processSerialCommand(String command) {
  command.trim();
  command.toUpperCase();

  // Send immediate acknowledgment for timing measurement
  Serial.print("ACK:");
  Serial.println(command);
  Serial.flush(); // Ensure immediate transmission

  if (command.startsWith("SET_PWM")) {
    // Format: SET_PWM,channel,value
    int firstComma = command.indexOf(',');
    int secondComma = command.indexOf(',', firstComma + 1);

    if (firstComma != -1 && secondComma != -1) {
      int channel = command.substring(firstComma + 1, secondComma).toInt();
      int value = command.substring(secondComma + 1).toInt();

      if (channel >= 0 && channel < 3 && value >= 0 && value <= 255) {
        currentPwmValues[channel] = value;
        switch(channel) {
          case 0: analogWrite(RED_LED_PIN, value); break;
          case 1: analogWrite(BLUE_LED_PIN, value); break;
          case 2: analogWrite(GREEN_LED_PIN, value); break;
        }
        sendResponse("SET_PWM_OK");
      } else {
        sendResponse("ERROR: Invalid parameters");
      }
    } else {
      sendResponse("ERROR: Invalid format");
    }
  }
  else if (command == "GET_PWM") {
    sendPwmValues();
  }
  else if (command == "SINGLE_SHOOT") {
    digitalWrite(PULSE_PIN, HIGH);
    delay(50);
    digitalWrite(PULSE_PIN, LOW);
    sendResponse("SINGLE_SHOOT_OK");
  }
  else if (command == "START_SHOOT") {
    continuousShootingActive = true;
    lastPulseTime = millis();
    sendResponse("START_SHOOT_OK");
  }
  else if (command == "STOP_SHOOT") {
    continuousShootingActive = false;
    digitalWrite(PULSE_PIN, LOW);
    sendResponse("STOP_SHOOT_OK");
  }
  else if (command.startsWith("SET_RATE")) {
    // Format: SET_RATE,rate
    int comma = command.indexOf(',');
    if (comma != -1) {
      int rate = command.substring(comma + 1).toInt();
      if (rate >= 0 && rate <= 10) {
        shootRateHz = rate;
        if (shootRateHz > 0) {
          pulseIntervalMillis = 1000 / shootRateHz;
        } else {
          continuousShootingActive = false;
          digitalWrite(PULSE_PIN, LOW);
          pulseIntervalMillis = 0;
        }
        sendResponse("SET_RATE_OK");
      } else {
        sendResponse("ERROR: Rate out of bounds (0-10)");
      }
    } else {
      sendResponse("ERROR: Invalid format");
    }
  }
  else if (command == "STEPPER_CW") {
    currentStepperMode = STEPPER_CW;
    sendResponse("STEPPER_CW_OK");
  }
  else if (command == "STEPPER_CCW") {
    currentStepperMode = STEPPER_CCW;
    sendResponse("STEPPER_CCW_OK");
  }
  else if (command == "STEPPER_STOP") {
    currentStepperMode = STEPPER_IDLE;
    sendResponse("STEPPER_STOP_OK");
  }
  else if (command.startsWith("STEPPER_ANGLE")) {
    // Format: STEPPER_ANGLE,angle
    int comma = command.indexOf(',');
    if (comma != -1) {
      float targetAngle = command.substring(comma + 1).toFloat();
      if (targetAngle >= 0 && targetAngle < 360) {
        currentStepperMode = STEPPER_IDLE;
        updateStepperAngleFromTotal();
        float angleDiff = targetAngle - currentStepperAngle;
        if (angleDiff > 180.0) angleDiff -= 360.0;
        else if (angleDiff < -180.0) angleDiff += 360.0;

        long stepsToMove = round((angleDiff / 360.0) * STEPS_PER_REVOLUTION_MOTOR);
        if (stepsToMove != 0) {
          myStepper.step(stepsToMove);
          stepperTotalSteps += stepsToMove;
        }
        updateStepperAngleFromTotal();
        sendResponse("STEPPER_ANGLE_OK");
      } else {
        sendResponse("ERROR: Angle out of bounds (0-359)");
      }
    } else {
      sendResponse("ERROR: Invalid format");
    }
  }
  else if (command.startsWith("STEPPER_SPEED")) {
    // Format: STEPPER_SPEED,rpm
    int comma = command.indexOf(',');
    if (comma != -1) {
      int rpm = command.substring(comma + 1).toInt();
      if (rpm >= 1 && rpm <= 20) {
        stepperSpeedRPM = rpm;
        myStepper.setSpeed(stepperSpeedRPM);
        sendResponse("STEPPER_SPEED_OK");
      } else {
        sendResponse("ERROR: RPM out of bounds (1-20)");
      }
    } else {
      sendResponse("ERROR: Invalid format");
    }
  }
  else if (command == "STEPPER_RESET") {
    currentStepperMode = STEPPER_IDLE;
    stepperTotalSteps = 0;
    updateStepperAngleFromTotal();
    sendResponse("STEPPER_RESET_OK");
  }
  else if (command == "GET_STEPPER") {
    sendStepperStatus();
  }
  else if (command.startsWith("RELAY_RIGHT_ON")) {
    // Format: RELAY_RIGHT_ON,timer (timer in seconds, 0 for manual)
    int comma = command.indexOf(',');
    unsigned int timerSeconds = relayRightTimerSettingSec; // Default

    if (comma != -1) {
      int parsedTimer = command.substring(comma + 1).toInt();
      if (parsedTimer >= 0 && parsedTimer <= 3600) {
        timerSeconds = (unsigned int)parsedTimer;
        relayRightTimerSettingSec = timerSeconds;
      } else {
        sendResponse("ERROR: Invalid timer value (0-3600)");
        return;
      }
    }

    digitalWrite(RELAY_RIGHT_PIN, LOW);   // LOW = ON for Active LOW relays
    relayRightActive = true;

    if (timerSeconds > 0) {
      relayRightDeactivationTime = millis() + (timerSeconds * 1000);
    } else {
      relayRightDeactivationTime = 0; // Manual mode
    }

    sendResponse("RELAY_RIGHT_ON_OK");
  }
  else if (command == "RELAY_RIGHT_OFF") {
    digitalWrite(RELAY_RIGHT_PIN, HIGH);  // HIGH = OFF for Active LOW relays
    relayRightActive = false;
    relayRightDeactivationTime = 0;
    sendResponse("RELAY_RIGHT_OFF_OK");
  }
  else if (command.startsWith("RELAY_LEFT_ON")) {
    // Format: RELAY_LEFT_ON,timer (timer in seconds, 0 for manual)
    int comma = command.indexOf(',');
    unsigned int timerSeconds = relayLeftTimerSettingSec; // Default

    if (comma != -1) {
      int parsedTimer = command.substring(comma + 1).toInt();
      if (parsedTimer >= 0 && parsedTimer <= 3600) {
        timerSeconds = (unsigned int)parsedTimer;
        relayLeftTimerSettingSec = timerSeconds;
      } else {
        sendResponse("ERROR: Invalid timer value (0-3600)");
        return;
      }
    }

    digitalWrite(RELAY_LEFT_PIN, LOW);    // LOW = ON for Active LOW relays
    relayLeftActive = true;

    if (timerSeconds > 0) {
      relayLeftDeactivationTime = millis() + (timerSeconds * 1000);
    } else {
      relayLeftDeactivationTime = 0; // Manual mode
    }

    sendResponse("RELAY_LEFT_ON_OK");
  }
  else if (command == "RELAY_LEFT_OFF") {
    digitalWrite(RELAY_LEFT_PIN, HIGH);   // HIGH = OFF for Active LOW relays
    relayLeftActive = false;
    relayLeftDeactivationTime = 0;
    sendResponse("RELAY_LEFT_OFF_OK");
  }
  else if (command == "GET_RELAY") {
    sendRelayStatus();
  }
  else if (command == "SAVE") {
    saveSettings();
    sendResponse("SAVE_OK");
  }
  else if (command == "RESET") {
    // Reset all to defaults
    for(int i=0; i<3; i++) currentPwmValues[i] = 0;
    shootRateHz = 1; pulseIntervalMillis = 1000; continuousShootingActive = false;
    currentStepperMode = STEPPER_IDLE; stepperTotalSteps = 0; stepperSpeedRPM = 12;
    myStepper.setSpeed(stepperSpeedRPM); updateStepperAngleFromTotal();

    // Reset Relays (HIGH = OFF for Active LOW relays)
    digitalWrite(RELAY_RIGHT_PIN, HIGH); relayRightActive = false; relayRightDeactivationTime = 0; relayRightTimerSettingSec = 5;
    digitalWrite(RELAY_LEFT_PIN, HIGH); relayLeftActive = false; relayLeftDeactivationTime = 0; relayLeftTimerSettingSec = 5;

    saveSettings();
    analogWrite(RED_LED_PIN, 0); analogWrite(BLUE_LED_PIN, 0); analogWrite(GREEN_LED_PIN, 0); digitalWrite(PULSE_PIN, LOW);
    digitalWrite(LED_MATRIX_PIN, LOW); // Reset LED Matrix

    sendResponse("RESET_OK");
  }
  // LED Matrix Commands
  else if (command == "LED_MATRIX_ON") {
    digitalWrite(LED_MATRIX_PIN, HIGH);
    sendResponse("LED_MATRIX_ON_OK");
  }
  else if (command == "LED_MATRIX_OFF") {
    digitalWrite(LED_MATRIX_PIN, LOW);
    sendResponse("LED_MATRIX_OFF_OK");
  }
  else if (command.startsWith("LED_MATRIX_PATTERN")) {
    // Format: LED_MATRIX_PATTERN,pattern_number
    int comma = command.indexOf(',');
    if (comma != -1) {
      int pattern = command.substring(comma + 1).toInt();
      // Simulate different patterns with blinking
      for (int i = 0; i < pattern + 1; i++) {
        digitalWrite(LED_MATRIX_PIN, HIGH);
        delay(100);
        digitalWrite(LED_MATRIX_PIN, LOW);
        delay(100);
      }
      digitalWrite(LED_MATRIX_PIN, HIGH); // Leave on after pattern
      sendResponse("LED_MATRIX_PATTERN_OK");
    } else {
      sendResponse("ERROR: Invalid pattern command");
    }
  }
  else if (command == "LED_MATRIX_BLINK") {
    // Blink test - 5 times
    for (int i = 0; i < 5; i++) {
      digitalWrite(LED_MATRIX_PIN, HIGH);
      delay(200);
      digitalWrite(LED_MATRIX_PIN, LOW);
      delay(200);
    }
    sendResponse("LED_MATRIX_BLINK_OK");
  }
  else if (command == "PING") {
    sendResponse("PONG");
  }
  else if (command == "INFO") {
    sendResponse("Arduino Controller v2.0 - Professional Dark Mode Compatible");
  }
  // Synchronized Flash-Camera Commands
  else if (command == "SYNC_PHOTO") {
    if (!syncSystemActive) {
      // Start synchronized photo sequence
      syncSystemActive = true;
      syncStartTime = millis();
      currentSyncState = SYNC_FLASH_TRIGGERED;

      // Trigger flash immediately
      digitalWrite(FLASH_TRIGGER_PIN, HIGH);

      sendResponse("SYNC_PHOTO_STARTED");
      Serial.print("FLASH_TRIGGERED_AT:");
      Serial.println(syncStartTime);
    } else {
      sendResponse("ERROR: Sync system already active");
    }
  }
  else if (command.startsWith("SET_FLASH_DELAY")) {
    // Format: SET_FLASH_DELAY,delay_ms
    int comma = command.indexOf(',');
    if (comma != -1) {
      unsigned long delay_ms = command.substring(comma + 1).toInt();
      if (delay_ms >= 0 && delay_ms <= 1000) {
        flashPreDelay = delay_ms;
        sendResponse("FLASH_DELAY_SET_OK");
      } else {
        sendResponse("ERROR: Delay out of bounds (0-1000ms)");
      }
    } else {
      sendResponse("ERROR: Invalid format");
    }
  }
  else if (command.startsWith("SET_CAMERA_DELAY")) {
    // Format: SET_CAMERA_DELAY,delay_ms
    int comma = command.indexOf(',');
    if (comma != -1) {
      unsigned long delay_ms = command.substring(comma + 1).toInt();
      if (delay_ms >= 50 && delay_ms <= 500) {
        cameraTriggerDelay = delay_ms;
        sendResponse("CAMERA_DELAY_SET_OK");
      } else {
        sendResponse("ERROR: Delay out of bounds (50-500ms)");
      }
    } else {
      sendResponse("ERROR: Invalid format");
    }
  }
  else if (command.startsWith("SET_FLASH_DURATION")) {
    // Format: SET_FLASH_DURATION,duration_ms
    int comma = command.indexOf(',');
    if (comma != -1) {
      unsigned long duration_ms = command.substring(comma + 1).toInt();
      if (duration_ms >= 10 && duration_ms <= 500) {
        flashDuration = duration_ms;
        sendResponse("FLASH_DURATION_SET_OK");
      } else {
        sendResponse("ERROR: Duration out of bounds (10-500ms)");
      }
    } else {
      sendResponse("ERROR: Invalid format");
    }
  }
  else if (command == "GET_SYNC_STATUS") {
    String response = "SYNC_STATUS:";
    response += syncSystemActive ? "ACTIVE" : "IDLE";
    response += ",FLASH_DELAY:" + String(flashPreDelay);
    response += ",CAMERA_DELAY:" + String(cameraTriggerDelay);
    response += ",FLASH_DURATION:" + String(flashDuration);
    response += ",CAMERA_DURATION:" + String(cameraDuration);
    sendResponse(response);
  }
  else if (command == "SYNC_TEST_FLASH") {
    // Test flash only
    digitalWrite(FLASH_TRIGGER_PIN, HIGH);
    delay(flashDuration);
    digitalWrite(FLASH_TRIGGER_PIN, LOW);
    sendResponse("FLASH_TEST_OK");
  }
  else if (command == "SYNC_TEST_CAMERA") {
    // Test camera only
    digitalWrite(CAMERA_TRIGGER_PIN, HIGH);
    delay(cameraDuration);
    digitalWrite(CAMERA_TRIGGER_PIN, LOW);
    sendResponse("CAMERA_TEST_OK");
  }
  else {
    sendResponse("ERROR: Unknown command");
  }
}


